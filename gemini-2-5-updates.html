<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini 2.5：我们最智能的模型变得更加强大</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>Gemini 2.5：我们最智能的模型变得更加强大</h1>
            <div class="author">Tulsee Doshi</div>
            <div class="date">产品管理高级总监，代表Gemini团队 | 2025年5月20日</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "Gemini 2.5 Pro继续受到开发者的喜爱，是编程领域的最佳模型，而2.5 Flash正在通过新的更新变得更加出色。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p>在Google I/O 2025大会上，我们分享了<span class="emphasis">Gemini 2.5</span>模型系列的更新，以及<span class="emphasis">Deep Think</span>——2.5 Pro的实验性增强推理模式。</p>
            </div>

            <!-- 第一部分：性能表现 -->
            <div class="section">
                <h2>2.5 Pro性能表现前所未有</h2>
                
                <p>除了在学术基准测试中的强劲表现外，新的<span class="highlight">2.5 Pro</span>现在在热门编程排行榜<span class="highlight">WebDev Arena</span>中领先，ELO得分达到<span class="stat-number">1415</span>分。它还在<span class="highlight">LMArena</span>的所有排行榜中领先，该平台评估各个维度的人类偏好。</p>

                <!-- 卡片网格 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">编程能力领先</div>
                        <div class="card-body">
                            <p>在WebDev Arena编程排行榜中排名第一，ELO得分<span class="stat-number">1415</span>分，展现了卓越的代码生成和调试能力。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">长上下文理解</div>
                        <div class="card-body">
                            <p>拥有<span class="stat-number">100万</span>token的上下文窗口，在长上下文和视频理解性能方面达到了最先进水平。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow);">教育领域优势</div>
                        <div class="card-body">
                            <p>融合LearnLM后，2.5 Pro现在是<span class="highlight">学习领域的领先模型</span>，在教育专家的对比评估中表现优异。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 第二部分：Deep Think -->
            <div class="section">
                <h2>Deep Think：增强推理模式</h2>
                
                <p>通过探索Gemini思维能力的前沿，我们开始测试一种名为<span class="emphasis">Deep Think</span>的增强推理模式，该模式使用新的研究技术，使模型在响应前能够考虑多个假设。</p>
                
                <div class="key-point">
                    <p>2.5 Pro Deep Think在<span class="highlight">2025年USAMO</span>（目前最难的数学基准之一）上取得了令人印象深刻的成绩，在<span class="highlight">LiveCodeBench</span>竞赛级编程基准中领先，在<span class="highlight">MMMU</span>多模态推理测试中得分<span class="stat-number">84.0%</span>。</p>
                </div>
            </div>

            <!-- 第三部分：2.5 Flash改进 -->
            <div class="section">
                <h2>更优秀的2.5 Flash</h2>
                
                <p><span class="highlight">2.5 Flash</span>是我们最高效的工作马模型，专为速度和低成本而设计——现在它在许多维度上都变得更好。它在推理、多模态、代码和长上下文的关键基准测试中都有所改进，同时变得更加高效，在我们的评估中使用的token减少了<span class="stat-number">20-30%</span>。</p>
                
                <ol>
                    <li><span class="highlight">效率提升：</span>在保持高质量的同时，token使用量减少20-30%</li>
                    <li><span class="highlight">多维度改进：</span>在推理、多模态处理、代码生成和长上下文理解方面全面提升</li>
                    <li><span class="highlight">广泛可用：</span>现已在Gemini应用、Google AI Studio和Vertex AI中提供预览版</li>
                </ol>
            </div>

            <!-- 第四部分：新功能 -->
            <div class="section">
                <h2>Gemini 2.5的新功能</h2>
                
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header">原生音频输出</div>
                        <div class="card-body">
                            <p>Live API引入了音视频输入和原生音频输出对话的预览版本，让您可以直接构建对话体验，获得更自然和富有表现力的Gemini。</p>
                            <ul>
                                <li>情感对话：检测用户语音中的情感并适当回应</li>
                                <li>主动音频：忽略背景对话，知道何时回应</li>
                                <li>支持<span class="stat-number">24+</span>种语言的文本转语音</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">计算机使用能力</div>
                        <div class="card-body">
                            <p>我们将<span class="highlight">Project Mariner</span>的计算机使用功能引入Gemini API和Vertex AI，让AI能够直接与计算机界面交互。</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">增强安全性</div>
                        <div class="card-body">
                            <p>显著增强了对安全威胁的防护，特别是间接提示注入攻击。新的安全方法大幅提高了Gemini在工具使用期间的保护率。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第五部分：开发者体验 -->
            <div class="section">
                <h2>增强的开发者体验</h2>
                
                <h4>思维摘要</h4>
                <p>2.5 Pro和Flash现在将在Gemini API和Vertex AI中包含思维摘要。思维摘要将模型的原始思维整理成清晰的格式，包含标题、关键细节和模型操作信息。</p>
                
                <h4>思维预算</h4>
                <p>我们将思维预算功能扩展到2.5 Pro，让开发者通过平衡延迟和质量来更好地控制成本。这允许您控制模型在响应前用于思考的token数量。</p>
                
                <h4>MCP支持</h4>
                <p>我们在Gemini API中添加了对模型上下文协议(MCP)定义的原生SDK支持，以便更轻松地与开源工具集成。</p>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "我们将继续在新方法上创新，以改进我们的模型和开发者体验，包括使它们更高效和高性能，并继续响应开发者的反馈。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Google | Google I/O 2025</p>
        </div>
    </div>
</body>
</html>
